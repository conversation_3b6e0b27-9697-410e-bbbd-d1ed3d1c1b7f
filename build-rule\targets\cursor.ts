import * as path from "path";
import { TargetConfig } from "../common/types";
import { FileUtils } from "../common/file-utils";

/**
 * Cursor 目标构建配置
 */
export class CursorTarget {
  static getConfig(): TargetConfig {
    return {
      name: 'Cursor',
      outputDir: '.cursor/rules',
      fileExtension: '.mdc',
      metadataFormat: 'yaml', // Cursor 使用 YAML 头部
      description: 'Cursor 编辑器规则目录'
    };
  }



  /**
   * 构建到指定项目
   */
  static async buildToProject(
    sourceDir: string,
    projectPath: string,
    isCurrentProject: boolean = false
  ): Promise<void> {
    const config = this.getConfig();
    const targetDir = path.join(projectPath, config.outputDir);
    const parentDir = path.dirname(targetDir);

    console.log(`📦 构建 ${config.name} 目标到: ${projectPath}`);

    // 如果不是当前项目，清理现有目录
    if (!isCurrentProject) {
      FileUtils.cleanDirectory(parentDir);
    }

    // 创建目录
    FileUtils.ensureDirectory(parentDir);
    FileUtils.ensureDirectory(targetDir);

    // 复制文件（源文件已经是Cursor格式，直接复制）
    const result = FileUtils.copyFiles({
      sourceDir,
      targetDir,
      fileExtension: config.fileExtension,
      metadataFormat: 'none' // 不添加元数据头部
    });

    console.log(`✅ ${config.name} 构建完成！共处理 ${result.copiedCount} 个文件`);

    // 如果不是当前项目，更新 .gitignore
    if (!isCurrentProject) {
      this.updateGitignore(projectPath);
    }
  }

  /**
   * 更新 .gitignore 文件
   */
  private static updateGitignore(projectPath: string): void {
    const rules = [
      "# Ignore all dotfiles except .vscode",
      ".*",
      "!.vscode/",
      "!.gitignore"
    ];

    FileUtils.updateGitignore(projectPath, rules);
  }


}
