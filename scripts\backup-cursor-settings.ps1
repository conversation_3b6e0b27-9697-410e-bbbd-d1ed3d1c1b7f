﻿# Cursor设置备份脚本
# 备份Cursor的设置、快捷键、代码片段等配置文件（不包含扩展）

param(
    [string]$BackupPath = "cursor/backup"
)

# 设置控制台编码为UTF-8以支持中文输出
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 开始备份Cursor设置..." -ForegroundColor Green

# 获取当前时间作为备份文件夹名称
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$backupDir = Join-Path $BackupPath $timestamp

# Cursor配置文件路径
$cursorUserPath = "$env:APPDATA\Cursor\User"

# 检查Cursor配置目录是否存在
if (-not (Test-Path $cursorUserPath)) {
    Write-Host "❌ 错误：找不到Cursor配置目录: $cursorUserPath" -ForegroundColor Red
    Write-Host "请确保Cursor已安装并至少运行过一次。" -ForegroundColor Yellow
    exit 1
}

# 创建备份目录
try {
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    Write-Host "📁 创建备份目录: $backupDir" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 错误：无法创建备份目录: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 定义要备份的文件和文件夹
$itemsToBackup = @(
    @{ Source = "settings.json"; Type = "File"; Description = "用户设置" },
    @{ Source = "keybindings.json"; Type = "File"; Description = "快捷键配置" },
    @{ Source = "snippets"; Type = "Directory"; Description = "代码片段" },
    @{ Source = "tasks.json"; Type = "File"; Description = "任务配置" },
    @{ Source = "launch.json"; Type = "File"; Description = "调试配置" }
)

$successCount = 0
$totalCount = 0

# 备份配置文件
foreach ($item in $itemsToBackup) {
    $sourcePath = Join-Path $cursorUserPath $item.Source
    $destPath = Join-Path $backupDir $item.Source
    $totalCount++
    
    if (Test-Path $sourcePath) {
        try {
            if ($item.Type -eq "Directory") {
                # 复制文件夹
                Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                $fileCount = (Get-ChildItem -Path $sourcePath -Recurse -File).Count
                Write-Host "✅ 已备份 $($item.Description): $($item.Source) ($fileCount 个文件)" -ForegroundColor Green
            } else {
                # 复制文件
                Copy-Item -Path $sourcePath -Destination $destPath -Force
                $fileSize = [math]::Round((Get-Item $sourcePath).Length / 1KB, 2)
                Write-Host "✅ 已备份 $($item.Description): $($item.Source) ($fileSize KB)" -ForegroundColor Green
            }
            $successCount++
        } catch {
            Write-Host "⚠️  备份 $($item.Description) 失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⏭️  跳过 $($item.Description): 文件不存在" -ForegroundColor Gray
    }
}

# 创建备份信息文件
$backupInfo = @{
    BackupTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    CursorVersion = "Unknown"
    BackupItems = $itemsToBackup | ForEach-Object { 
        $sourcePath = Join-Path $cursorUserPath $_.Source
        @{
            Name = $_.Source
            Description = $_.Description
            Exists = Test-Path $sourcePath
            Type = $_.Type
        }
    }
    SuccessCount = $successCount
    TotalCount = $totalCount
}

$backupInfoPath = Join-Path $backupDir "backup-info.json"
$backupInfo | ConvertTo-Json -Depth 3 | Out-File -FilePath $backupInfoPath -Encoding UTF8

Write-Host "`n📊 备份完成统计:" -ForegroundColor Magenta
Write-Host "   备份时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host "   备份位置: $backupDir" -ForegroundColor White
Write-Host "   成功备份: $successCount/$totalCount 项" -ForegroundColor White

if ($successCount -eq $totalCount) {
    Write-Host "`n🎉 所有配置文件备份成功！" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  部分配置文件备份成功，请检查上述输出。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 备份失败，请检查错误信息。" -ForegroundColor Red
    exit 1
}

Write-Host "`n💡 提示：您可以在 '$backupDir' 目录中找到备份的配置文件。" -ForegroundColor Cyan
