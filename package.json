{"name": "cursor-mcp-rule", "version": "1.0.0", "description": "MCP智能调用规则和开发规范文档", "main": "index.js", "repository": "*************:cjh-1996/cursor-rule.git", "author": "cjh", "scripts": {"git:push": "git add -A && git-pro commit && git pull && git push", "install:mcp": "powershell -ExecutionPolicy Bypass -File ./scripts/install-mcp-plugins.ps1", "backup:cursor": "powershell -ExecutionPolicy Bypass -File ./scripts/backup-cursor-settings.ps1", "build:rules": "ts-node ./build-rule/build.ts", "compile:rules": "tsc", "type-check": "tsc --noEmit"}, "dependencies": {"@cjh0/git-pro": "latest", "inquirer": "^12.8.2"}, "keywords": ["mcp", "cursor", "development-rules", "vue3"], "license": "MIT", "devDependencies": {"@types/inquirer": "^9.0.8", "@types/node": "^24.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}