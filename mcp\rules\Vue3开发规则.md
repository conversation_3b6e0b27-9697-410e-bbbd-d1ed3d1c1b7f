---
alwaysApply: false
description: "这是vue3项目开发规范，如果开发的vue3项目，则需要读取规则"
---

# Cursor AI 开发助手规范 (中文回答问题)

## 🎯 核心技术栈

- **前端框架**: Vue 3 (Composition API) + TypeScript
- **包管理器**: yarn@1.22.22
- **工具库生态**:
  - lodash (通过 `$g._` 访问)
  - VueUse
  - TailwindCSS
- **自动引入**: `vue`、`pinia`、`vue-router`、`@vueuse/core` 已配置,无需手动 import

## 📋 代码风格规范

### Vue.js 开发规范

- **API 风格**: 统一使用 Composition API
- **语法糖**: 使用 `<script setup>` 语法
- **组件规范**: 严格遵循 Vue.js 官方风格指南
- **响应式数据**: 优先使用 `$ref` 语法糖定义
- **组件参数**:

```typescript
let props = withDefaults(defineProps<IProps>(), {
  showLoading: false,
});
```

### TypeScript 类型规范

- **类型文件管理**: 简单类型直接放在当前文件,复杂类型单独管理
- **命名规范**:
  - `interface`: 大驼峰 + I 前缀 (如 `IUserProps`)
  - `type`: 大驼峰 + T 前缀 (如 `TButtonSize`)
  - `enum`: 使用中文命名,见名知意
- **注释要求**: 所有类型定义必须包含中文注释

### 样式系统规范

- **TailwindCSS**:
  - spacing 系统支持 1-1000px,**优先使用 px 单位** (如 `w-100px`)
  - **不使用主题色**,颜色优先使用自定义颜色 `bg-[#fff]`
- **SCSS Deep 语法**: 使用嵌套式写法

```scss
:deep() {
  .class {
    // 样式
  }
}
```

- **图标系统**: 支持 remixicon 按需引入

```vue
- **unplugin-icons**：标签格式`svg-图标前缀-图标名称
<svg-ri-account-box-line class="text-40px"></svg-ri-account-box-line>
` - **g-icon 组件**：支持 remixicon，配置 name、size、color
<g-icon name="ri-arrow-down-line" size="18" color="" />
```

## 🔧 函数与方法规范

### 函数定义优先级

1. **顶层函数使用声明式** (推荐):

```typescript
/** 处理提交逻辑 */
function handleSubmit(): void {
  // ...
}
```

2. **Vue 生命周期**:

```typescript
onMounted(() => {
  // 初始化逻辑
});
```

3. **计算属性与侦听器**:

```typescript
const computedValue = computed(() => {
  // 计算逻辑
});

watch(source, () => {
  // 监听逻辑
});
```

### 异步函数规范

- **必须要求**:
  - 使用 `async/await` 语法
  - 包含 `try/catch` 错误处理
  - 明确返回值类型声明

```typescript
/**
 * 获取用户信息
 * @param userId 用户ID
 * @returns 用户数据或错误信息
 */
async function fetchUserInfo(userId: string): Promise<IUserInfo | null> {
  try {
    const response = await api.getUserInfo(userId);
    return response;
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return null;
  }
}
```

## 📝 注释标准体系

### 变量注释 (简洁型)

```typescript
// 加载状态标识
let loading = $ref(false);

// 用户列表数据
let userList = $ref<IUser[]>([]);
```

### 函数注释层级

**Level 1: 简单函数**

```typescript
/** 切换显示状态 */
function toggleVisible(): void {}
```

**Level 2: 中等复杂度**

```typescript
/**
 * 处理表单提交
 * @param form 表单数据
 */
function handleSubmit(form: IFormData): void {}
```

**Level 3: 复杂函数**

```typescript
/**
 * 处理用户数据提交并进行校验
 * @param {IUserForm} form - 用户表单数据
 * @param {boolean} autoSave - 是否自动保存
 * @returns {Promise<ISubmitResult>} 提交处理结果
 * @throws {ValidationError} 表单验证错误
 * @example
 * const result = await handleUserSubmit({
 *   name: '张三',
 *   email: '<EMAIL>'
 * }, true)
 */
async function handleUserSubmit(
  form: IUserForm,
  autoSave = false
): Promise<ISubmitResult> {}
```

## 🏗️ 状态管理策略

### 状态管理选择

- **简单状态**: 使用 `provide/inject`
- **组件内状态**: 使用 `$ref` 语法糖
- **复杂全局状态**: 使用 Pinia (需包含中文注释)

### Pinia Store 规范

```typescript
interface IUserState {
  /** 用户信息 */
  userInfo: IUserInfo | null;
  /** 登录状态 */
  isLoggedIn: boolean;
}

export const useUserStore = defineStore("user", () => {
  // 状态定义
  const userInfo = $ref<IUserInfo | null>(null);
  const isLoggedIn = computed(() => !!userInfo);

  /** 用户登录 */
  async function login(credentials: ILoginForm): Promise<boolean> {
    // 登录逻辑
  }

  return {
    userInfo: $$(userInfo),
    isLoggedIn,
    login,
  };
});
```

## 📁 项目架构组织

### 目录结构规范

```
src/
├── components/          # 通用组件
├── views/              # 页面组件
├── composables/        # 组合式函数
├── utils/              # 工具函数
├── types/              # 类型定义
├── api/                # API 请求
├── stores/             # Pinia 状态管理
├── constants/          # 常量配置
└── assets/             # 静态资源
```

### 文件命名规范

- **组件**: PascalCase (如 `UserProfile.vue`)
- **页面**: PascalCase (如 `UserCenter.vue`)
- **工具函数**: camelCase (如 `formatDate.ts`)
- **类型文件**: camelCase (如 `userTypes.ts`)
- **常量文件**: camelCase (如 `apiConfig.ts`)

## 🚀 性能与优化

### 代码优化策略

- **工具库优先级**: lodash > VueUse > 自定义实现
- **组件懒加载**: 使用 `defineAsyncComponent`
- **计算属性缓存**: 合理使用 `computed`
- **事件防抖节流**: 使用 VueUse 的 `useDebounceFn`、`useThrottleFn`

### ESLint 配置

- 使用 ESLint 推荐规则
- 禁用 `console.log` 在生产环境
- 强制使用 TypeScript 严格模式

## 📊 开发优先级

### P0 级 (必须遵守)

- ✅ TypeScript 类型定义完整
- ✅ 函数声明规范遵循
- ✅ 异步函数错误处理
- ✅ 基础开发环境配置
- ✅ 关键注释覆盖

### P1 级 (强烈建议)

- 🔸 统一命名规范
- 🔸 状态管理规范
- 🔸 组件拆分合理性
- 🔸 API 请求统一管理

### P2 级 (推荐遵守)

- 🔹 性能优化实施
- 🔹 代码格式化一致性
- 🔹 测试用例覆盖
- 🔹 文档完善程度

## 🎯 代码质量检查清单

### 提交前检查

- [ ] TypeScript 类型无警告
- [ ] ESLint 检查通过
- [ ] 关键函数包含注释
- [ ] 异步操作有错误处理
- [ ] 组件性能无明显问题
- [ ] 样式符合设计规范

### Code Review 重点

- [ ] 业务逻辑清晰
- [ ] 错误处理完善
- [ ] 类型定义准确
- [ ] 组件职责单一
- [ ] 性能优化合理

---
