---
alwaysApply: true
---

# 开发环境配置说明

## 系统环境

- **操作系统**: Windows 11 (Build 26100)
- **命令行工具**: PowerShell 7

### 版本控制

- **Git**: 最新版本
- **提交规范**: 使用 yarn git:push 命令
- **git 本地操作-MCP 工具**: git-mcp-server

### Node.js 环境

- **Node.js**: 20.19.3
- **包管理器**: yarn@1.22.22
- **配置文件**: package.json

## 项目规则文件

- **MCP 智能调用规则.md** - MCP 工具使用规范 (P0 级)
- **AI 角色人格.md** - AI 智能编程伙伴角色定位 (P0 级)
- **开发环境说明.md** - 本文件，环境配置记录
- **Vue3 开发规则.md** - Vue3 项目开发规范

## 注意事项

- 除非用户明确要求，否则不做 git 相关操作
- Git 操作强制使用 git-mcp-server 工具（推送代码除外）
- 复杂问题优先使用 sequential-thinking 分析
- 当你读取规则文件时，请按照如下格式提示我,用几个显示几个（必须）
  ------规则使用中------
  [📄 xxx.md][📄 xxx.md] [📄 xxx.md]
- 所有回复和思考都使用中文
- 要求遵守 KISS 原则 不要增加无用的代码
- 当回复涉及架构、流程、数据关系、时间线、复杂逻辑，或明确要求流程图时，使用彩色 Mermaid 回复。
- 永远不要道歉，不要说"抱歉"、"对不起"等客套话
