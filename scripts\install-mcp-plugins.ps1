﻿# MCP Plugin Installer (PowerShell)
# Save this file with UTF-8 encoding

# Set console encoding
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "MCP Plugin Installer"

# Color output functions
function Write-Success { param([string]$Text) Write-Host "✅ $Text" -ForegroundColor Green }
function Write-Error { param([string]$Text) Write-Host "❌ $Text" -ForegroundColor Red }
function Write-Warning { param([string]$Text) Write-Host "⚠️  $Text" -ForegroundColor Yellow }
function Write-Info { param([string]$Text) Write-Host "ℹ️  $Text" -ForegroundColor Cyan }
function Write-Step { param([string]$Text) Write-Host "🔧 $Text" -ForegroundColor Blue }

# Helper function for Y/N prompts with default Y
function Read-HostWithDefault {
    param([string]$Prompt, [string]$Default = "Y")
    $input = Read-Host "$Prompt (默认: $Default)"
    if ([string]::IsNullOrWhiteSpace($input)) {
        return $Default
    }
    return $input
}

Clear-Host

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    MCP 插件一键安装脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Info "检查系统环境..."

# Check Node.js
$nodeExists = $false
try {
    $nodeVersion = & node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Node.js $nodeVersion 检查通过"
        $nodeExists = $true
    }
}
catch {
    $nodeExists = $false
}

if (-not $nodeExists) {
    Write-Error "Node.js 未安装"
    Write-Warning "请从 https://nodejs.org 下载并安装 Node.js"
    Read-Host "按回车键退出"
    exit 1
}

# Check npm
$npmExists = $false
try {
    $npmVersion = & npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "npm $npmVersion 检查通过"
        $npmExists = $true
    }
}
catch {
    $npmExists = $false
}

if (-not $npmExists) {
    Write-Error "npm 未安装"
    Write-Warning "npm 通常随 Node.js 一起安装，请重新安装 Node.js"
    Read-Host "按回车键退出"
    exit 1
}

Write-Info "Python服务将通过uvx自动运行，无需预安装"
Write-Host ""

# Package list
$packages = @(
    "context7-mcp|@upstash/context7-mcp@latest|上下文管理服务",
    "git-mcp-server|@cyanheads/git-mcp-server|Git操作服务",
    "mcp-server-sequential-thinking|@modelcontextprotocol/server-sequential-thinking|顺序思维服务",
    "browser-tools-mcp|@agentdeskai/browser-tools-mcp@latest|浏览器工具服务",
    "fetcher-mcp|fetcher-mcp|数据获取服务",
    "browser-tools-server|@agentdeskai/browser-tools-server@latest|浏览器工具服务器"
)

$totalPackages = $packages.Count
Write-Warning "即将安装 $totalPackages 个 MCP 插件"

# Ask user to continue with default Y
$continue = Read-HostWithDefault "是否继续安装? (Y/N)" "Y"

if ($continue -in @('N', 'n', 'No', 'no')) {
    Write-Warning "安装已取消"
    Read-Host "按回车键退出"
    exit 0
}

Write-Host ""
Write-Info "开始安装 MCP 插件..."
Write-Host ""

# Install packages
$installedCount = 0
$failedCount = 0
$failedPackages = @()

for ($i = 0; $i -lt $packages.Count; $i++) {
    $packageInfo = $packages[$i].Split('|')
    $displayName = $packageInfo[0]
    $packageName = $packageInfo[1]
    $description = $packageInfo[2]
    
    $currentStep = $i + 1
    Write-Step "[$currentStep/$totalPackages] 正在安装 $displayName ($description)..."
    
    try {
        # Check if already installed
        $checkResult = & npm list -g $displayName 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Warning "$displayName 已安装，跳过"
            $installedCount++
            Write-Host ""
            continue
        }
        
        # Install package
        $installOutput = & npm install -g $packageName 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "$displayName 安装成功"
            $installedCount++
        }
        else {
            Write-Error "$displayName 安装失败"
            $failedCount++
            $failedPackages += "$displayName|$packageName"
        }
    }
    catch {
        Write-Error "$displayName 安装出现异常: $_"
        $failedCount++
        $failedPackages += "$displayName|$packageName"
    }
    
    Write-Host ""
}

# Installation summary
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "           安装完成总结" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if ($failedCount -eq 0) {
    Write-Success "🎉 所有插件安装成功！"
}
else {
    Write-Warning "⚠️  安装完成，但有 $failedCount 个插件安装失败"
}

Write-Host "📊 安装统计:" -ForegroundColor White
Write-Success "   成功: $installedCount/$totalPackages"
if ($failedCount -gt 0) {
    Write-Error "   失败: $failedCount/$totalPackages"
    Write-Host ""
    Write-Host "❌ 失败的插件:" -ForegroundColor Red
    foreach ($failed in $failedPackages) {
        $failedInfo = $failed.Split('|')
        Write-Host "   • $($failedInfo[0])" -ForegroundColor Red
        Write-Host "     手动安装命令: npm install -g $($failedInfo[1])" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📦 已安装的 Node.js MCP 插件:" -ForegroundColor White
Write-Success "   • context7-mcp (上下文管理)"
Write-Success "   • git-mcp-server (Git操作)"
Write-Success "   • mcp-server-sequential-thinking (顺序思维)"
Write-Success "   • browser-tools-mcp (浏览器工具)"
Write-Success "   • fetcher-mcp (数据获取)"
Write-Success "   • browser-tools-server (浏览器工具服务器)"

Write-Host ""
Write-Host "📋 无需安装的服务:" -ForegroundColor White
Write-Info "   • mcp-feedback-enhanced (通过uvx自动运行)"
Write-Info "   • wikipedia-mcp (通过uvx自动运行)"
Write-Info "   • github-mcp (基于URL的服务)"

Write-Host ""
Write-Host "💡 有用的命令:" -ForegroundColor Cyan
Write-Host "   • 查看已安装包: npm list -g --depth=0" -ForegroundColor White
Write-Host "   • 更新包: npm update -g [包名]" -ForegroundColor White
Write-Host "   • 卸载包: npm uninstall -g [包名]" -ForegroundColor White

Write-Host ""
Write-Success "感谢使用 MCP 插件安装工具！"
Read-Host "按回车键退出"