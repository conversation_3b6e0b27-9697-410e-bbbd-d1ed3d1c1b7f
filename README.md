# MCP 插件管理工具

这个项目提供了一键安装所有必需 MCP 插件的脚本和配置文件。

## 🚀 快速开始

### 方法 1：使用 npm 命令（推荐）

```bash
# PowerShell版本（推荐，功能最完整）
npm run install:mcp

# 批处理版本（备选方案）
npm run install:mcp-detailed
```

### 方法 2：直接运行脚本

```powershell
# PowerShell脚本（推荐）
.\scripts\install.ps1
```

```cmd
# 批处理脚本（备选）
.\scripts\install-mcp-plugins.bat
```

### 方法 3：手动安装

```bash
# 复制以下命令到终端执行
npm install -g @upstash/context7-mcp@latest @cyanheads/git-mcp-server @modelcontextprotocol/server-sequential-thinking @agentdeskai/browser-tools-mcp@latest fetcher-mcp @agentdeskai/browser-tools-server@latest
```

## 📦 安装的插件列表

### 需要安装的 Node.js 包：

- `@upstash/context7-mcp@latest` - 上下文管理服务
- `@cyanheads/git-mcp-server` - Git 操作服务
- `@modelcontextprotocol/server-sequential-thinking` - 顺序思维服务
- `@agentdeskai/browser-tools-mcp@latest` - 浏览器工具服务
- `fetcher-mcp` - 数据获取服务
- `@agentdeskai/browser-tools-server@latest` - 浏览器工具服务器

### 通过 uvx 自动运行（无需预安装）：

- `mcp-feedback-enhanced@latest` - 交互反馈增强服务
- `wikipedia-mcp@latest` - 维基百科服务

### 无需安装的服务：

- `github-mcp` - 基于 URL 的 GitHub 服务

## 🌟 脚本特点

### PowerShell 版本 (`install.ps1`) - 推荐

- ✅ 完整的中文界面和提示
- ✅ 系统环境检查（Node.js、npm 版本）
- ✅ 安装前确认和进度显示
- ✅ 重复安装检测（跳过已安装的包）
- ✅ 详细的错误处理和失败重试建议
- ✅ 安装统计和总结报告
- ✅ 自动打开 Claude Desktop 配置目录选项
- ✅ 有用的管理命令提示

### 批处理版本 (`install-mcp-plugins.bat`) - 备选

- ✅ 简单直接的安装流程
- ✅ 基本的错误检查
- ✅ 英文界面避免编码问题

## 📋 配置步骤

1. **安装插件**：运行 `npm run install:mcp`
2. **复制配置**：将 `mcp/MCP配置(全局安装).json` 复制到 Claude Desktop 配置目录：
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Linux: `~/.config/claude/claude_desktop_config.json`
3. **重启应用**：重启 Claude Desktop 应用
4. **验证服务**：确认所有 MCP 服务正常工作

## 🚀 智能部署到其他项目

### 使用构建脚本部署规则

```bash
# 构建规则文件到当前项目
npm run build:rules

# 部署规则文件（等同于build:rules）
npm run deploy:rules
```

**构建脚本功能：**

- 🔍 **自动扫描**：扫描同级目录下的所有项目文件夹
- 📋 **交互选择**：提供友好的项目选择界面
- 💾 **智能记忆**：记住上次选择的项目，下次默认选中
- 📁 **自动部署**：将规则文件复制到目标项目的 `.augment/rules` 目录
- 📄 **配置同步**：自动复制 `.gitignore` 文件到目标项目（备份原文件）
- 📝 **文档生成**：自动生成项目说明文档和使用指南

**使用流程：**

1. 运行 `npm run build:rules` 或 `npm run deploy:rules`
2. **步骤 1**：自动构建到当前项目的 `.augment/rules` 目录
3. **步骤 2**：询问是否需要部署到其他项目
   - 选择"否"：仅构建当前项目，完成
   - 选择"是"：继续选择其他项目进行部署
4. 如选择部署到其他项目：
   - 从项目列表中选择目标项目（支持方向键导航）
   - 默认选中上次选择的项目（智能记忆）
   - 自动创建目标项目的 `.augment/rules` 目录
   - 复制所有规则文件并生成专属文档
   - 自动复制 `.gitignore` 文件（原文件备份为 `.gitignore.backup`）

**输出结构：**

```
# 当前项目 (cursor-mcp-rule)
cursor-mcp-rule/
└── .augment/
    ├── README.md           # 项目说明文档
    └── rules/
        ├── MCP智能调用规则.md
        ├── 开发环境说明.md
        └── Vue3开发规则.md

# 其他项目 (如果选择部署)
目标项目/
└── .augment/
    ├── README.md           # 项目专属说明文档
    └── rules/
        ├── MCP智能调用规则.md
        ├── 开发环境说明.md
        └── Vue3开发规则.md
```

## 🛠️ 项目结构

```
cursor-mcp-rule/
├── scripts/
│   ├── install.ps1                # PowerShell安装脚本（推荐）
│   ├── install-mcp-plugins.bat    # 批处理安装脚本（备选）
│   └── backup-cursor-settings.ps1 # Cursor设置备份脚本
├── build-rule/
│   ├── build.ts                   # 智能规则构建脚本
│   ├── .build-config.json         # 构建配置（本地，已忽略）
│   ├── common/                    # 通用工具类
│   └── targets/                   # 构建目标配置
├── mcp/
│   ├── MCP配置(全局安装).json      # MCP服务配置文件
│   └── rules/
│       ├── MCP智能调用规则.md      # MCP工具使用规范
│       ├── 开发环境说明.md         # 环境配置说明
│       └── Vue3开发规则.md         # Vue3开发规范
├── package.json                   # 项目配置和脚本
└── README.md                      # 本文件
```

## 🔧 故障排除

### 如果 npm 安装失败：

- 检查 Node.js 版本：`node --version`
- 更新 npm：`npm install -g npm@latest`
- 清理缓存：`npm cache clean --force`

### 如果批处理脚本执行问题：

- 确保以管理员身份运行命令提示符
- 检查脚本文件编码是否为 UTF-8

### 如果权限问题：

- Windows：以管理员身份运行终端
- 配置 npm 全局目录：`npm config set prefix ~/.npm-global`

## 📚 相关文档

- [MCP 智能调用规则](./rules/MCP智能调用规则.md) - 详细的 MCP 工具使用规范
- [开发环境说明](./rules/开发环境说明.md) - 开发环境配置详情
- [开发规则](./rules/开发规则.md) - 项目开发规范

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License
