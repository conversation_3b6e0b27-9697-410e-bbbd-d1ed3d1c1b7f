# Ignore all dotfiles except .vscode
.*
!.vscode/
!.gitignore

# Build configuration (local only)
build-rule/.build-config.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?


*.tsbuildinfo

version.json
types/auto-imports.d.ts
types/components.d.ts

stats.html

vite.config.ts.timestamp*