import * as fs from "fs";
import * as path from "path";
import { BuildConfig, BuildTarget } from "./types";

/**
 * 配置管理工具类
 */
export class ConfigUtils {
  private configFile: string;

  constructor(projectRoot: string) {
    this.configFile = path.join(projectRoot, "build-rule", ".build-config.json");
  }

  /**
   * 加载配置
   */
  loadConfig(): BuildConfig {
    try {
      if (fs.existsSync(this.configFile)) {
        const config = JSON.parse(fs.readFileSync(this.configFile, "utf-8"));
        return config as BuildConfig;
      }
    } catch (error) {
      console.log("⚠️ 读取配置文件失败，使用默认配置");
    }
    return {};
  }

  /**
   * 保存配置
   */
  saveConfig(targetProject: string, targets: BuildTarget[]): void {
    const config: BuildConfig = {
      lastProject: targetProject,
      lastTargets: targets,
      lastBuildTime: new Date().toISOString(),
    };

    try {
      fs.writeFileSync(
        this.configFile,
        JSON.stringify(config, null, 2),
        "utf-8"
      );
      console.log("💾 配置已保存");
    } catch (error) {
      console.log("⚠️ 保存配置失败:", (error as Error).message);
    }
  }

  /**
   * 获取项目根目录
   */
  static getProjectRoot(): string {
    // 检测是否在 TypeScript 源码目录还是编译后的目录
    const isCompiledVersion = __dirname.includes('dist');

    if (isCompiledVersion) {
      // 从编译后的 dist/build-rule/common 目录回到项目根目录
      return path.join(__dirname, "../../../");
    } else {
      // 从源码 build-rule/common 目录回到项目根目录
      return path.join(__dirname, "../../");
    }
  }

  /**
   * 获取工作目录（项目根目录的上级目录）
   */
  static getWorkDir(): string {
    const projectRoot = this.getProjectRoot();
    return path.dirname(projectRoot);
  }

  /**
   * 扫描同级目录下的项目
   */
  static scanProjects(): string[] {
    const workDir = this.getWorkDir();
    
    try {
      const items = fs.readdirSync(workDir, { withFileTypes: true });
      const projects = items
        .filter((item) => item.isDirectory() && item.name !== "cursor-mcp-rule")
        .map((item) => item.name)
        .sort();

      if (projects.length === 0) {
        throw new Error("未找到任何项目目录");
      }

      projects.forEach((project, index) => {
        console.log(`   ${index + 1}. ${project}`);
      });
      console.log();

      return projects;
    } catch (error) {
      throw new Error(`扫描项目目录失败: ${(error as Error).message}`);
    }
  }
}
