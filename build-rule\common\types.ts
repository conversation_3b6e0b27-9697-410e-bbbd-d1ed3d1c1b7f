/**
 * 构建系统类型定义
 */

export interface BuildConfig {
  lastProject?: string;
  lastBuildTime?: string;
  lastTargets?: BuildTarget[];
}

export interface ProjectChoice {
  name: string;
  value: string;
}

export type BuildTarget = 'augment' | 'cursor';

export interface TargetConfig {
  name: string;
  outputDir: string;
  fileExtension: string;
  metadataFormat: 'yaml' | 'json' | 'none';
  description: string;
}

export interface FileMetadata {
  type?: 'always_apply' | 'agent_requested';
  description?: string;
  [key: string]: any;
}

export interface BuildOptions {
  targets: BuildTarget[];
  projectPath?: string;
  deployToOther?: boolean;
}

export interface CopyResult {
  copiedCount: number;
  skippedCount: number;
  errors: string[];
}
